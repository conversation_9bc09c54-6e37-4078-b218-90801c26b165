class ApiConfig {
  // 账号系统服务器URL（使用本地CloudBase测试服务器）
  static const String baseUrl = 'http://19***********:3001/api';
  static const String wsUrl = 'ws://19***********:3001/ws'; // WebSocket地址

  // 备用CloudBase配置
  // static const String baseUrl = 'https://your-cloudbase-env.service.tcloudbase.com/api';
  // static const String wsUrl = 'wss://your-cloudbase-env.service.tcloudbase.com/ws';

  // 原有的小说生成服务器URL
  static const String novelApiUrl = 'https://www.dznovel.top'; // 主域名（更可靠）
  static const String backupUrl = 'http://*************:8000'; // 备用IP地址
  // static const String novelApiUrl = 'http://localhost:8000'; // 开发环境

  // 获取服务器连接超时时间（秒）
  static const int connectionTimeout = 120; // 增加到2分钟
  static const int receiveTimeout = 120;    // 增加到2分钟
  static const int sendTimeout = 120;       // 增加到2分钟

  // API端点配置
  static const Map<String, String> endpoints = {
    // 认证相关
    'sendCode': '/auth/send-code',
    'verifyCode': '/auth/verify-code',
    'register': '/auth/register',
    'login': '/auth/login',
    'logout': '/auth/logout',
    'refreshToken': '/auth/refresh',

    // 用户相关
    'userProfile': '/user/profile',
    'userPassword': '/user/password',
    'userAvatar': '/user/avatar',
    'userSettings': '/user/settings',
    'deleteAccount': '/user/account',

    // 数据同步
    'syncUpload': '/sync/upload',
    'syncDownload': '/sync/download',

    // 支付相关
    'packages': '/packages',
    'createOrder': '/orders/create',
    'myOrders': '/orders/my',
    'cancelOrder': '/orders/{id}/cancel',
    'wechatPay': '/payment/wechat',
    'memberCodePay': '/payment/member-code',
    'paymentStatus': '/payment/status/{id}',

    // 会员码相关
    'validateMemberCode': '/member-code/validate',
  };

  /// 获取API端点URL
  static String getEndpoint(String key, [Map<String, String>? params]) {
    String endpoint = endpoints[key] ?? '';
    if (params != null) {
      params.forEach((key, value) {
        endpoint = endpoint.replaceAll('{$key}', value);
      });
    }
    return '$baseUrl$endpoint';
  }
}
