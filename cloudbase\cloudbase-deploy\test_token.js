const jwt = require('jsonwebtoken');

// 使用与服务器相同的SECRET_KEY
const SECRET_KEY = 'novel-app-secret-key-2024';

// 测试用户ID
const testUserId = 'cdccfad1-72bf-4407-85f3-5ba1d7aa7eca';

console.log('🔧 Token测试开始...');
console.log('SECRET_KEY:', SECRET_KEY);
console.log('测试用户ID:', testUserId);

// 生成token
function generateToken(userId) {
  return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '24h' });
}

// 验证token
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, SECRET_KEY);
    return { success: true, decoded };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// 测试token生成和验证
const token = generateToken(testUserId);
console.log('\n✅ 生成的Token:', token);

const verification = verifyToken(token);
console.log('\n🔍 Token验证结果:', verification);

// 测试HTTP请求
const http = require('http');

const postData = JSON.stringify({
  username: '23456',
  password: '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92' // 123456的SHA256
});

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/auth/login',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('\n🚀 测试登录请求...');
const req = http.request(options, (res) => {
  console.log(`状态码: ${res.statusCode}`);
  console.log(`响应头: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('响应数据:', data);
    
    try {
      const response = JSON.parse(data);
      if (response.success && response.data.token) {
        const loginToken = response.data.token;
        console.log('\n✅ 登录成功，获得Token:', loginToken);
        
        // 验证登录获得的token
        const loginVerification = verifyToken(loginToken);
        console.log('🔍 登录Token验证结果:', loginVerification);
        
        // 测试同步上传请求
        testSyncUpload(loginToken);
      }
    } catch (e) {
      console.log('解析响应失败:', e.message);
    }
  });
});

req.on('error', (e) => {
  console.error(`请求遇到问题: ${e.message}`);
});

req.write(postData);
req.end();

// 测试同步上传
function testSyncUpload(token) {
  console.log('\n🔄 测试同步上传...');
  
  const uploadData = JSON.stringify({
    data: {
      novels: [],
      characters: [],
      timestamp: new Date().toISOString()
    }
  });
  
  const uploadOptions = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/sync/upload',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Content-Length': Buffer.byteLength(uploadData)
    }
  };
  
  const uploadReq = http.request(uploadOptions, (res) => {
    console.log(`同步上传状态码: ${res.statusCode}`);
    
    let uploadResponseData = '';
    res.on('data', (chunk) => {
      uploadResponseData += chunk;
    });
    
    res.on('end', () => {
      console.log('同步上传响应:', uploadResponseData);
    });
  });
  
  uploadReq.on('error', (e) => {
    console.error(`同步上传请求遇到问题: ${e.message}`);
  });
  
  uploadReq.write(uploadData);
  uploadReq.end();
}
