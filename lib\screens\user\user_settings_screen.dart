import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../models/user.dart';
import '../../services/auth_service.dart';
import '../../services/network_test_service.dart';
import '../auth/login_screen.dart';
import 'membership_screen.dart';

/// 用户设置页面
class UserSettingsScreen extends StatelessWidget {
  const UserSettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final UserController userController = Get.find<UserController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('账号设置'),
        elevation: 0,
      ),
      body: Obx(() {
        final user = userController.currentUser.value;
        
        if (!userController.isLoggedIn.value || user == null) {
          return _buildLoginPrompt();
        }

        return SingleChildScrollView(
          child: Column(
            children: [
              // 用户信息卡片
              _buildUserInfoCard(user, userController),
              
              const SizedBox(height: 16),
              
              // 设置选项
              _buildSettingsSection(user, userController),
            ],
          ),
        );
      }),
    );
  }

  /// 构建登录提示
  Widget _buildLoginPrompt() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.account_circle_outlined,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            '请先登录账号',
            style: TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              Get.to(() => const LoginScreen());
            },
            child: const Text('立即登录'),
          ),
        ],
      ),
    );
  }

  /// 构建用户信息卡片
  Widget _buildUserInfoCard(User user, UserController controller) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          // 头像
          GestureDetector(
            onTap: controller.selectAndUploadAvatar,
            child: Stack(
              children: [
                Obx(() {
                  final currentUser = controller.currentUser.value;
                  final avatarUrl = currentUser?.avatar;
                  final isUpdating = controller.isUpdatingProfile.value;

                  return Stack(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
                            ? NetworkImage(avatarUrl)
                            : null,
                        child: avatarUrl == null || avatarUrl.isEmpty
                            ? const Icon(Icons.person, size: 40)
                            : null,
                      ),
                      if (isUpdating)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              shape: BoxShape.circle,
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            ),
                          ),
                        ),
                    ],
                  );
                }),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 用户名
          Text(
            user.username,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 会员状态
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: user.isValidMember ? Colors.orange : Colors.grey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              controller.getMembershipStatusText(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 手机号
          Text(
            user.phoneNumber,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设置选项
  Widget _buildSettingsSection(User user, UserController controller) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          // 修改用户名
          _buildSettingItem(
            icon: Icons.edit,
            title: '修改用户名',
            subtitle: user.username,
            onTap: () => _showEditUsernameDialog(controller),
          ),
          
          const Divider(height: 1),
          
          // 修改密码
          _buildSettingItem(
            icon: Icons.lock_outline,
            title: '修改密码',
            subtitle: '保护账号安全',
            onTap: () => _showChangePasswordDialog(controller),
          ),
          
          const Divider(height: 1),
          
          // 会员中心
          _buildSettingItem(
            icon: Icons.diamond,
            title: '会员中心',
            subtitle: controller.getMembershipStatusText(),
            onTap: () => Get.to(() => const MembershipScreen()),
          ),
          
          const Divider(height: 1),
          
          // 数据同步
          Obx(() {
            final user = controller.currentUser.value;
            final isMember = user?.isValidMember ?? false;
            return _buildSwitchItem(
              icon: Icons.sync,
              title: '数据同步',
              subtitle: isMember
                  ? (controller.isSyncEnabled.value ? '已开启' : '已关闭')
                  : '仅限会员使用',
              value: isMember ? controller.isSyncEnabled.value : false,
              onChanged: isMember ? (bool value) {
                controller.toggleDataSync(value);
              } : null,
            );
          }),
          
          const Divider(height: 1),
          
          // 生物识别
          Obx(() {
            final currentUser = controller.currentUser.value;
            if (currentUser == null) return const SizedBox.shrink();

            return _buildSwitchItem(
              icon: Icons.fingerprint,
              title: '生物识别登录',
              subtitle: currentUser.settings.enableBiometric ? '已开启' : '已关闭',
              value: currentUser.settings.enableBiometric,
              onChanged: (value) => _toggleBiometric(controller, value),
            );
          }),
          
          const Divider(height: 1),
          
          // 手动同步
          Obx(() {
            final user = controller.currentUser.value;
            final isMember = user?.isValidMember ?? false;
            return _buildSettingItem(
              icon: Icons.cloud_sync,
              title: '手动同步',
              subtitle: isMember
                  ? (controller.lastSyncTime.value != null
                      ? '上次同步: ${_formatTime(controller.lastSyncTime.value!)}'
                      : '从未同步')
                  : '仅限会员使用',
              onTap: isMember && !controller.isSyncing.value ? controller.manualSync : null,
              trailing: isMember && controller.isSyncing.value
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : null,
            );
          }),
          
          const Divider(height: 1),
          
          // 导出数据
          _buildSettingItem(
            icon: Icons.download,
            title: '导出数据',
            subtitle: '备份您的数据',
            onTap: controller.exportUserData,
          ),

          const Divider(height: 1),

          // 网络连接测试
          _buildSettingItem(
            icon: Icons.network_check,
            title: '网络连接测试',
            subtitle: '检查服务器连接状态',
            onTap: _performNetworkTest,
          ),

          const Divider(height: 1),

          // 退出登录
          _buildSettingItem(
            icon: Icons.logout,
            title: '退出登录',
            subtitle: '退出当前账号',
            onTap: () => _showLogoutDialog(controller),
            textColor: Colors.red,
          ),
          
          const Divider(height: 1),
          
          // 注销账号
          _buildSettingItem(
            icon: Icons.delete_forever,
            title: '注销账号',
            subtitle: '永久删除账号和数据',
            onTap: controller.deleteAccount,
            textColor: Colors.red,
          ),
        ],
      ),
    );
  }

  /// 构建设置项
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: textColor),
      title: Text(
        title,
        style: TextStyle(color: textColor),
      ),
      subtitle: Text(subtitle),
      trailing: trailing ?? const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  /// 构建开关项
  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    ValueChanged<bool>? onChanged,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  /// 显示修改用户名对话框
  void _showEditUsernameDialog(UserController controller) {
    final textController = TextEditingController(
      text: controller.currentUser.value?.username ?? '',
    );
    
    Get.dialog(
      AlertDialog(
        title: const Text('修改用户名'),
        content: TextField(
          controller: textController,
          decoration: const InputDecoration(
            labelText: '新用户名',
            hintText: '请输入新用户名',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.updateUsername(textController.text.trim());
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  /// 显示修改密码对话框
  void _showChangePasswordDialog(UserController controller) {
    final oldPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    
    Get.dialog(
      AlertDialog(
        title: const Text('修改密码'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: oldPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: '当前密码',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: newPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: '新密码',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.updatePassword(
                oldPasswordController.text,
                newPasswordController.text,
              );
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  /// 切换生物识别
  void _toggleBiometric(UserController controller, bool value) async {
    final user = controller.currentUser.value;
    if (user != null) {
      final settings = user.settings;
      settings.enableBiometric = value;
      await controller.updateSettings(settings);
    }
  }

  /// 显示退出登录对话框
  void _showLogoutDialog(UserController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              try {
                Get.find<AuthService>().logout();
              } catch (e) {
                print('退出登录失败: $e');
                Get.snackbar('错误', '退出登录失败');
              }
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.month}/${time.day} ${time.hour}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 执行网络连接测试
  void _performNetworkTest() async {
    // 显示加载对话框
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('正在测试网络连接...'),
          ],
        ),
        content: Text('请稍候，正在检查服务器连接状态'),
      ),
      barrierDismissible: false,
    );

    try {
      // 执行网络测试
      final results = await NetworkTestService.testServerConnection();

      // 关闭加载对话框
      Get.back();

      // 显示测试结果
      NetworkTestService.showTestResults(results);
    } catch (e) {
      // 关闭加载对话框
      Get.back();

      // 显示错误信息
      Get.dialog(
        AlertDialog(
          title: Text('网络测试失败'),
          content: Text('网络测试过程中发生错误：$e'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('确定'),
            ),
          ],
        ),
      );
    }
  }
}
