# 手机连接问题解决方案

## 问题描述
手机端Flutter应用无法连接到本地服务器，出现连接超时错误：
```
DioException [connection timeout]: The request connection took longer than 0:01:00.000000
```

## 解决步骤

### 1. 确认网络连接
确保手机和电脑连接到同一个WiFi网络：
- 电脑IP地址：`*************`
- 服务器端口：`3001`
- 服务器地址：`http://*************:3001`

### 2. 配置Windows防火墙
运行以下命令（以管理员身份运行PowerShell）：
```powershell
# 允许端口3001的入站连接
netsh advfirewall firewall add rule name="Novel App API Server - Port 3001" dir=in action=allow protocol=TCP localport=3001

# 允许端口3001的出站连接
netsh advfirewall firewall add rule name="Novel App API Server - Port 3001 Out" dir=out action=allow protocol=TCP localport=3001
```

或者运行项目根目录下的 `setup_firewall.bat` 文件。

### 3. 验证服务器状态
确认服务器正在运行：
```bash
# 检查端口是否监听
netstat -an | findstr :3001

# 应该看到类似输出：
# TCP    0.0.0.0:3001           0.0.0.0:0              LISTENING
```

### 4. 测试网络连接
在手机上使用浏览器访问：
```
http://*************:3001/api/health
```

如果能看到JSON响应，说明网络连接正常。

### 5. 使用应用内网络测试
在Flutter应用中：
1. 进入"账号设置"页面
2. 点击"网络连接测试"
3. 查看测试结果

### 6. 常见问题排查

#### 问题1：防火墙阻止连接
**症状**：连接超时，无法访问服务器
**解决**：按照步骤2配置防火墙规则

#### 问题2：IP地址不匹配
**症状**：连接被拒绝
**解决**：
1. 在电脑上运行 `ipconfig` 查看实际IP地址
2. 更新 `lib/config/api_config.dart` 中的 `baseUrl`

#### 问题3：服务器未启动
**症状**：连接被拒绝
**解决**：
1. 进入 `cloudbase/cloudbase-deploy` 目录
2. 运行 `node cloudbase-server.js`

#### 问题4：端口被占用
**症状**：服务器启动失败
**解决**：
1. 查找占用端口的进程：`netstat -ano | findstr :3001`
2. 结束占用进程或更换端口

### 7. 高级解决方案

#### 使用端口转发
如果防火墙配置复杂，可以使用端口转发：
```bash
netsh interface portproxy add v4tov4 listenport=3001 listenaddress=0.0.0.0 connectport=3001 connectaddress=127.0.0.1
```

#### 临时关闭防火墙（不推荐）
仅用于测试：
```bash
netsh advfirewall set allprofiles state off
```
测试完成后记得重新开启：
```bash
netsh advfirewall set allprofiles state on
```

## 验证解决方案

### 方法1：浏览器测试
在手机浏览器中访问：
- 健康检查：`http://*************:3001/api/health`
- 应该返回JSON格式的服务器状态信息

### 方法2：应用内测试
1. 打开Flutter应用
2. 进入"账号设置"
3. 点击"网络连接测试"
4. 查看所有测试项目是否通过

### 方法3：登录测试
1. 使用测试账号登录：
   - 用户名：`23456`
   - 密码：`123456`
2. 如果登录成功，说明网络连接正常

## 技术细节

### 服务器配置
- 绑定地址：`0.0.0.0`（接受所有网络接口的连接）
- 监听端口：`3001`
- API前缀：`/api`

### 客户端配置
- 连接超时：120秒
- 接收超时：120秒
- 发送超时：120秒

### 网络要求
- 手机和电脑必须在同一局域网
- 防火墙必须允许端口3001的连接
- 路由器不能阻止设备间通信

## 联系支持
如果以上方案都无法解决问题，请提供以下信息：
1. 网络测试结果截图
2. 服务器启动日志
3. 手机和电脑的IP地址
4. 错误信息的完整日志
