const http = require('http');

console.log('🚀 快速连接测试开始...\n');

// 测试配置
const tests = [
  {
    name: '本地连接测试 (localhost:3001)',
    hostname: 'localhost',
    port: 3001,
    path: '/api/health'
  },
  {
    name: '内网IP连接测试 (*************:3001)',
    hostname: '*************',
    port: 3001,
    path: '/api/health'
  },
  {
    name: '错误端口测试 (localhost:3000) - 应该失败',
    hostname: 'localhost',
    port: 3000,
    path: '/api/health'
  }
];

// 执行测试
async function runTest(test) {
  return new Promise((resolve) => {
    console.log(`🔍 ${test.name}`);
    
    const options = {
      hostname: test.hostname,
      port: test.port,
      path: test.path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      console.log(`   ✅ 连接成功 - 状态码: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log(`   📊 服务器: ${response.service} v${response.version}`);
          console.log(`   ⏰ 时间戳: ${response.timestamp}`);
        } catch (e) {
          console.log(`   📄 响应: ${data.substring(0, 100)}...`);
        }
        resolve({ success: true, statusCode: res.statusCode });
      });
    });

    req.on('error', (err) => {
      console.log(`   ❌ 连接失败: ${err.message}`);
      resolve({ success: false, error: err.message });
    });

    req.on('timeout', () => {
      console.log(`   ⏰ 连接超时`);
      req.destroy();
      resolve({ success: false, error: 'timeout' });
    });

    req.end();
  });
}

// 运行所有测试
async function runAllTests() {
  for (const test of tests) {
    await runTest(test);
    console.log(''); // 空行分隔
  }
  
  console.log('🏁 测试完成');
  console.log('\n📋 总结:');
  console.log('   - 如果 localhost:3001 和 *************:3001 都成功，说明服务器配置正确');
  console.log('   - 如果 localhost:3000 失败，说明端口配置问题已修复');
  console.log('   - 手机应该能够通过 *************:3001 访问服务器');
}

runAllTests().catch(console.error);
