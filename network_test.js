const http = require('http');

// 测试服务器连接
function testConnection(host, port, path = '/api/health') {
  return new Promise((resolve, reject) => {
    console.log(`🔍 测试连接: http://${host}:${port}${path}`);
    
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      console.log(`✅ 连接成功 - 状态码: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📄 响应内容: ${data}`);
        resolve({ success: true, statusCode: res.statusCode, data });
      });
    });

    req.on('error', (err) => {
      console.log(`❌ 连接失败: ${err.message}`);
      resolve({ success: false, error: err.message });
    });

    req.on('timeout', () => {
      console.log(`⏰ 连接超时`);
      req.destroy();
      resolve({ success: false, error: 'timeout' });
    });

    req.end();
  });
}

async function runTests() {
  console.log('🚀 开始网络连接测试...\n');
  
  // 测试本地连接
  console.log('1. 测试本地连接 (localhost)');
  await testConnection('localhost', 3001);
  console.log('');
  
  // 测试内网IP连接
  console.log('2. 测试内网IP连接 (*************)');
  await testConnection('*************', 3001);
  console.log('');
  
  // 测试登录API
  console.log('3. 测试登录API');
  await testLoginAPI();
  console.log('');
  
  console.log('🏁 测试完成');
}

async function testLoginAPI() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      username: '23456',
      password: '8d969eef6ecad3c29a3a629280e686cf0c3f5d5a86aff3ca12020c923adc6c92'
    });

    const options = {
      hostname: '*************',
      port: 3001,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 10000
    };

    console.log(`🔍 测试登录API: http://*************:3001/api/auth/login`);

    const req = http.request(options, (res) => {
      console.log(`✅ 登录API响应 - 状态码: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.success) {
            console.log(`🎉 登录成功 - 用户: ${response.data.user.username}`);
          } else {
            console.log(`❌ 登录失败 - 消息: ${response.message}`);
          }
        } catch (e) {
          console.log(`📄 原始响应: ${data}`);
        }
        resolve();
      });
    });

    req.on('error', (err) => {
      console.log(`❌ 登录API请求失败: ${err.message}`);
      resolve();
    });

    req.on('timeout', () => {
      console.log(`⏰ 登录API请求超时`);
      req.destroy();
      resolve();
    });

    req.write(postData);
    req.end();
  });
}

// 运行测试
runTests().catch(console.error);
