const fs = require('fs');

console.log('🔍 检查小说数据结构...');

try {
  const db = JSON.parse(fs.readFileSync('data/db.json', 'utf8'));
  const syncRecord = db.syncData.find(s => s.userId === '87ca175d-227c-4afe-95ef-1249b7d7cd3f');
  
  if (!syncRecord) {
    console.log('❌ 未找到用户同步记录');
    return;
  }
  
  if (!syncRecord.data || !syncRecord.data.novels) {
    console.log('❌ 同步记录中没有小说数据');
    return;
  }
  
  const novels = syncRecord.data.novels;
  console.log(`📚 找到 ${novels.length} 本小说`);
  console.log('');
  
  // 检查前3本小说的结构
  novels.slice(0, 3).forEach((novel, index) => {
    console.log(`${index + 1}. 小说: ${novel.title || '无标题'}`);
    console.log(`   ID: ${novel.id || '无ID'}`);
    console.log(`   作者: ${novel.author || '无作者'}`);
    console.log(`   内容长度: ${novel.content?.length || 0} 字`);
    console.log(`   章节数: ${novel.chapters?.length || 0} 章`);
    console.log(`   创建时间: ${novel.createdAt || '无时间'}`);
    console.log(`   字段列表: ${Object.keys(novel).join(', ')}`);
    console.log('');
  });
  
  // 检查是否有必要字段缺失
  const firstNovel = novels[0];
  const requiredFields = ['id', 'title', 'content', 'createdAt'];
  const missingFields = requiredFields.filter(field => !firstNovel[field]);
  
  if (missingFields.length > 0) {
    console.log(`⚠️ 缺失字段: ${missingFields.join(', ')}`);
  } else {
    console.log('✅ 所有必要字段都存在');
  }
  
} catch (error) {
  console.error('❌ 检查失败:', error.message);
}
